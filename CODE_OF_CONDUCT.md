# Code of Conduct

## Our Pledge

We pledge to make participation in the SuperClaude community a welcoming experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

## Our Standards

### Positive Behavior
- Being respectful and inclusive
- Providing constructive feedback
- Focusing on what is best for the community
- Showing empathy towards other community members
- Helping newcomers learn and contribute

### Unacceptable Behavior
- Harassment, trolling, or discriminatory language
- Personal attacks or political arguments
- Publishing private information without permission
- Spam or off-topic content
- Any conduct that could reasonably be considered inappropriate

## Responsibilities

Project maintainers are responsible for clarifying standards of acceptable behavior and are expected to take appropriate and fair corrective action in response to any instances of unacceptable behavior.

## Scope

This Code of Conduct applies within all project spaces, including:
- GitHub repository (issues, PRs, discussions)
- Project documentation
- Community forums and chat
- Project events and meetups

## Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be reported by contacting the project team through GitHub issues. All complaints will be reviewed and investigated and will result in a response that is deemed necessary and appropriate to the circumstances.

## Attribution

This Code of Conduct is adapted from the [Contributor Covenant](https://www.contributor-covenant.org/), version 2.0.

---

*SuperClaude v2 | Building a respectful community*