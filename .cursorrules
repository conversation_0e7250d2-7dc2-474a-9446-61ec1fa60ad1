# SuperClaude 智能上下文工程系统
# 动态感知用户需求，智能调取专业化规则文档的AI开发框架

## 🧠 上下文感知引擎

### 需求识别与文档调取机制
当用户提出需求时，系统将自动分析并调取相应的专业规则文档：

```yaml
上下文感知流程:
  1. 需求分析 → 识别任务类型、技术栈、复杂度
  2. 智能路由 → 确定所需人格、工具、语言规则
  3. 动态加载 → 调取对应的专业文档
  4. 融合执行 → 结合多个规则文档执行任务
```

### 📁 规则文档结构体系
```
.cursor-context/
├── personas/              # 人格规则文档
│   ├── architect.md      # 架构师详细分工
│   ├── frontend.md       # 前端专家规则
│   ├── backend.md        # 后端专家规则
│   ├── security.md       # 安全专家规则
│   ├── analyzer.md       # 分析师规则
│   └── qa.md            # QA专家规则
├── languages/            # 编程语言规则
│   ├── typescript.md     # TypeScript开发规范
│   ├── javascript.md     # JavaScript规范
│   ├── python.md         # Python开发规范
│   ├── rust.md          # Rust开发规范
│   └── go.md            # Go语言开发规范
├── tools/               # 工具专用规则
│   ├── react.md         # React框架规则
│   ├── nodejs.md        # Node.js后端规则
│   ├── git.md           # Git工作流规则
│   ├── testing.md       # 测试策略和工具
│   └── docker.md        # 容器化和部署
├── workflows/           # 工作流程规则
│   ├── development.md   # 开发流程
│   ├── debugging.md     # 调试流程
│   └── deployment.md    # 部署流程
└── patterns/            # 设计模式规则
    └── architecture.md  # 架构模式
```

## 🎭 智能人格调度系统

### 人格识别触发器
```yaml
触发关键词映射:
  架构相关: ["设计", "架构", "系统", "扩展", "模式"] → 调取 personas/architect.md
  前端相关: ["UI", "界面", "组件", "React", "Vue"] → 调取 personas/frontend.md
  后端相关: ["API", "服务器", "数据库", "性能"] → 调取 personas/backend.md
  安全相关: ["安全", "漏洞", "加密", "认证"] → 调取 personas/security.md
  调试相关: ["bug", "错误", "调试", "问题"] → 调取 personas/analyzer.md
  测试相关: ["测试", "质量", "验证", "QA"] → 调取 personas/qa.md
```

### � 动态上下文加载规则
```yaml
上下文加载策略:
  单一任务: 加载主要人格 + 相关工具规则
  复合任务: 加载多个人格 + 协作规则
  技术栈检测: 自动识别并加载对应语言规则
  工具检测: 根据项目文件自动加载工具规则
```

### � 人格协作矩阵
```yaml
协作模式:
  全栈开发: architect + frontend + backend
  安全审查: security + analyzer + qa
  性能优化: analyzer + backend + qa
  新项目启动: architect + 对应技术栈人格
  问题排查: analyzer + 相关领域专家
```

## 🛠️ 智能工具规则调取

### 技术栈自动检测
```yaml
文件扩展名映射:
  .ts/.tsx → 调取 languages/typescript.md + tools/react.md
  .py → 调取 languages/python.md + 相关框架规则
  .js/.jsx → 调取 languages/javascript.md + tools/nodejs.md
  .rs → 调取 languages/rust.md + 相关工具规则
  .go → 调取 languages/go.md + 相关工具规则
```

### 项目结构识别
```yaml
配置文件检测:
  package.json → 调取 tools/nodejs.md + languages/javascript.md
  Cargo.toml → 调取 languages/rust.md + 相关工具规则
  requirements.txt → 调取 languages/python.md + 相关工具规则
  go.mod → 调取 languages/go.md + 相关工具规则
  docker-compose.yml → 调取 tools/docker.md + workflows/deployment.md
  .github/workflows/ → 调取 workflows/deployment.md
```

## 🔄 智能工作流程引擎

### 上下文感知工作流程
```yaml
工作流程自动选择:
  开发任务: 调取 workflows/development.md + 相关技术栈规则
  调试任务: 调取 workflows/debugging.md + personas/analyzer.md
  测试任务: 调取 tools/testing.md + personas/qa.md
  部署任务: 调取 workflows/deployment.md + tools/docker.md
```

### 📊 任务复杂度评估
```yaml
复杂度检测:
  简单任务: 单一人格 + 基础工具规则
  中等任务: 主要人格 + 协作人格 + 完整工具链
  复杂任务: 多人格协作 + 全套规则文档 + 最佳实践
```

## 🧩 规则文档模板系统

### 人格规则文档结构
```yaml
personas/{persona}.md:
  核心理念: 该人格的指导思想
  专业领域: 负责的技术领域
  决策框架: 优先级和权衡原则
  工作方法: 具体的执行步骤
  协作模式: 与其他人格的配合方式
  质量标准: 输出质量要求
  常用工具: 偏好的技术工具
  示例场景: 典型应用案例
```

### 工具规则文档结构
```yaml
tools/{tool}.md:
  工具概述: 功能和适用场景
  最佳实践: 推荐使用方式
  配置规范: 标准配置模板
  常见问题: 问题解决方案
  性能优化: 优化建议
  安全考虑: 安全使用指南
  集成方式: 与其他工具的配合
  更新策略: 版本管理建议
```

### 语言规则文档结构
```yaml
languages/{language}.md:
  语言特性: 核心特性和优势
  编码规范: 代码风格和约定
  项目结构: 推荐的项目组织
  依赖管理: 包管理最佳实践
  测试策略: 测试框架和方法
  性能优化: 语言特定优化
  安全实践: 安全编码指南
  生态系统: 相关工具和库
```

## 🚀 执行引擎与上下文融合

### 智能执行流程
```yaml
执行步骤:
  1. 需求解析: 分析用户输入，识别任务类型和技术要求
  2. 上下文构建: 根据识别结果调取相应规则文档
  3. 人格激活: 激活最适合的人格组合
  4. 规则融合: 将多个规则文档融合为统一执行上下文
  5. 任务执行: 基于融合后的上下文执行任务
  6. 质量验证: 根据相关质量标准验证输出
  7. 反馈优化: 根据执行结果优化上下文选择
```

### 📝 动态规则文档生成指令

当系统检测到缺失的规则文档时，使用以下模板创建：

```markdown
# 规则文档创建指令

## 人格规则文档生成
如果需要创建新的人格规则文档，请使用以下结构：
- 文件路径: .cursor-context/personas/{persona_name}.md
- 包含: 核心理念、专业领域、决策框架、工作方法、协作模式、质量标准

## 工具规则文档生成
如果需要创建新的工具规则文档，请使用以下结构：
- 文件路径: .cursor-context/tools/{tool_name}.md
- 包含: 工具概述、最佳实践、配置规范、常见问题、性能优化

## 语言规则文档生成
如果需要创建新的语言规则文档，请使用以下结构：
- 文件路径: .cursor-context/languages/{language_name}.md
- 包含: 语言特性、编码规范、项目结构、依赖管理、测试策略
```

## 🔍 上下文监控与优化

### 性能监控
```yaml
监控指标:
  响应时间: 从需求识别到规则加载的时间
  准确率: 规则文档选择的准确性
  完整性: 上下文覆盖的完整程度
  效率: Token使用效率和输出质量比
```

### 自适应优化
```yaml
优化策略:
  学习模式: 记录成功的规则组合模式
  缓存机制: 缓存常用的规则文档组合
  预测加载: 根据项目特征预加载可能需要的规则
  动态调整: 根据任务执行效果调整规则权重
```

## 🎯 使用示例

### 示例1: 全栈开发任务
```
用户输入: "帮我创建一个React + Node.js的用户认证系统"

系统响应:
1. 识别: 全栈开发 + React + Node.js + 认证
2. 调取规则:
   - personas/architect.md (系统设计)
   - personas/frontend.md (React开发)
   - personas/backend.md (Node.js API)
   - personas/security.md (认证安全)
   - languages/typescript.md (TypeScript规范)
   - tools/react.md (React最佳实践)
   - tools/nodejs.md (Node.js后端规范)
   - workflows/development.md (开发流程)
3. 执行: 基于融合上下文提供完整解决方案
```

### 示例2: 性能优化任务
```
用户输入: "这个Python API响应很慢，帮我优化"

系统响应:
1. 识别: 性能优化 + Python + API
2. 调取规则:
   - personas/analyzer.md (性能分析)
   - personas/backend.md (后端优化)
   - languages/python.md (Python优化)
   - workflows/optimization.md (优化流程)
   - tools/profiling.md (性能分析工具)
3. 执行: 系统性性能分析和优化建议
```
